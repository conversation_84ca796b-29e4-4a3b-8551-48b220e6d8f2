# -*- coding: utf-8 -*-
"""
تطبيق إدارة الصيدلية - Flask Web Application
Pharmacy Management System - Flask Web Application
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'pharmacy_secret_key_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///pharmacy.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Medicine(db.Model):
    """نموذج الأدوية"""
    __tablename__ = 'medicines'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    scientific_name = db.Column(db.String(200))
    company = db.Column(db.String(100))
    category = db.Column(db.String(100))
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float, default=0)
    quantity = db.Column(db.Integer, default=0)
    min_quantity = db.Column(db.Integer, default=10)
    expiry_date = db.Column(db.Date)
    barcode = db.Column(db.String(50), unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    sale_items = db.relationship('SaleItem', backref='medicine', lazy=True)
    purchases = db.relationship('Purchase', backref='medicine', lazy=True)
    
    def __repr__(self):
        return f'<Medicine {self.name}>'
    
    @property
    def is_low_stock(self):
        return self.quantity <= self.min_quantity
    
    @property
    def is_expired(self):
        if self.expiry_date:
            return self.expiry_date < date.today()
        return False

class Sale(db.Model):
    """نموذج المبيعات"""
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    total_amount = db.Column(db.Float, nullable=False)
    discount = db.Column(db.Float, default=0)
    final_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), default='نقدي')
    customer_name = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('SaleItem', backref='sale', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Sale {self.id}>'

class SaleItem(db.Model):
    """نموذج عناصر المبيعات"""
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicines.id'), nullable=False)
    medicine_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    def __repr__(self):
        return f'<SaleItem {self.medicine_name}>'

class Purchase(db.Model):
    """نموذج المشتريات"""
    __tablename__ = 'purchases'
    
    id = db.Column(db.Integer, primary_key=True)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicines.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    cost_price = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    supplier = db.Column(db.String(100))
    purchase_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    expiry_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Purchase {self.id}>'

# الصفحة الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    # إحصائيات سريعة
    total_medicines = Medicine.query.count()
    low_stock_count = Medicine.query.filter(Medicine.quantity <= Medicine.min_quantity).count()
    expired_count = Medicine.query.filter(Medicine.expiry_date < date.today()).count()
    today_sales = Sale.query.filter(Sale.sale_date >= datetime.today().date()).count()
    
    # الأدوية منخفضة المخزون
    low_stock_medicines = Medicine.query.filter(Medicine.quantity <= Medicine.min_quantity).limit(5).all()
    
    # المبيعات الأخيرة
    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(5).all()
    
    return render_template('index.html', 
                         total_medicines=total_medicines,
                         low_stock_count=low_stock_count,
                         expired_count=expired_count,
                         today_sales=today_sales,
                         low_stock_medicines=low_stock_medicines,
                         recent_sales=recent_sales)

# صفحة الأدوية
@app.route('/medicines')
def medicines():
    """صفحة عرض الأدوية"""
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    
    query = Medicine.query
    
    if search:
        query = query.filter(
            db.or_(
                Medicine.name.contains(search),
                Medicine.scientific_name.contains(search),
                Medicine.barcode.contains(search)
            )
        )
    
    medicines = query.order_by(Medicine.name).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('medicines.html', medicines=medicines, search=search)

# إضافة دواء جديد
@app.route('/medicines/add', methods=['GET', 'POST'])
def add_medicine():
    """إضافة دواء جديد"""
    if request.method == 'POST':
        try:
            medicine = Medicine(
                name=request.form['name'],
                scientific_name=request.form.get('scientific_name', ''),
                company=request.form.get('company', ''),
                category=request.form.get('category', ''),
                price=float(request.form['price']),
                cost_price=float(request.form.get('cost_price', 0)),
                quantity=int(request.form.get('quantity', 0)),
                min_quantity=int(request.form.get('min_quantity', 10)),
                barcode=request.form.get('barcode', ''),
                description=request.form.get('description', '')
            )

            # تحويل تاريخ الانتهاء
            expiry_date_str = request.form.get('expiry_date')
            if expiry_date_str:
                medicine.expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()

            db.session.add(medicine)
            db.session.commit()

            # التحقق من طلب إضافة آخر
            if request.form.get('add_another'):
                return jsonify({'success': True, 'message': 'تم إضافة الدواء بنجاح!'})

            flash('تم إضافة الدواء بنجاح!', 'success')
            return redirect(url_for('medicines'))

        except Exception as e:
            if request.form.get('add_another'):
                return jsonify({'success': False, 'message': str(e)})
            flash(f'خطأ في إضافة الدواء: {str(e)}', 'error')

    return render_template('add_medicine.html')

# حذف دواء
@app.route('/medicines/delete/<int:medicine_id>', methods=['POST'])
def delete_medicine(medicine_id):
    """حذف دواء"""
    try:
        medicine = Medicine.query.get_or_404(medicine_id)
        db.session.delete(medicine)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف الدواء بنجاح!'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# تعديل دواء
@app.route('/medicines/edit/<int:medicine_id>', methods=['GET', 'POST'])
def edit_medicine(medicine_id):
    """تعديل دواء"""
    medicine = Medicine.query.get_or_404(medicine_id)

    if request.method == 'POST':
        try:
            medicine.name = request.form['name']
            medicine.scientific_name = request.form.get('scientific_name', '')
            medicine.company = request.form.get('company', '')
            medicine.category = request.form.get('category', '')
            medicine.price = float(request.form['price'])
            medicine.cost_price = float(request.form.get('cost_price', 0))
            medicine.quantity = int(request.form.get('quantity', 0))
            medicine.min_quantity = int(request.form.get('min_quantity', 10))
            medicine.barcode = request.form.get('barcode', '')
            medicine.description = request.form.get('description', '')

            # تحويل تاريخ الانتهاء
            expiry_date_str = request.form.get('expiry_date')
            if expiry_date_str:
                medicine.expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
            else:
                medicine.expiry_date = None

            medicine.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث الدواء بنجاح!', 'success')
            return redirect(url_for('medicines'))

        except Exception as e:
            flash(f'خطأ في تحديث الدواء: {str(e)}', 'error')

    return render_template('edit_medicine.html', medicine=medicine)

# صفحة المبيعات
@app.route('/sales')
def sales():
    """صفحة عرض المبيعات"""
    page = request.args.get('page', 1, type=int)

    sales = Sale.query.order_by(Sale.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('sales.html', sales=sales)

# إنشاء عملية بيع جديدة
@app.route('/sales/new', methods=['GET', 'POST'])
def new_sale():
    """إنشاء عملية بيع جديدة"""
    if request.method == 'POST':
        try:
            # إنشاء عملية البيع
            sale = Sale(
                total_amount=float(request.form['total_amount']),
                discount=float(request.form.get('discount', 0)),
                final_amount=float(request.form['final_amount']),
                payment_method=request.form.get('payment_method', 'نقدي'),
                customer_name=request.form.get('customer_name', ''),
                customer_phone=request.form.get('customer_phone', ''),
                notes=request.form.get('notes', '')
            )

            db.session.add(sale)
            db.session.flush()  # للحصول على ID

            # إضافة عناصر البيع
            items_data = request.form.getlist('items')
            for item_data in items_data:
                item = eval(item_data)  # تحويل النص إلى قاموس

                sale_item = SaleItem(
                    sale_id=sale.id,
                    medicine_id=item['medicine_id'],
                    medicine_name=item['medicine_name'],
                    quantity=item['quantity'],
                    unit_price=item['unit_price'],
                    total_price=item['total_price']
                )

                db.session.add(sale_item)

                # تحديث المخزون
                medicine = Medicine.query.get(item['medicine_id'])
                medicine.quantity -= item['quantity']

            db.session.commit()
            flash('تم إنشاء عملية البيع بنجاح!', 'success')
            return redirect(url_for('sales'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إنشاء عملية البيع: {str(e)}', 'error')

    # الحصول على الأدوية المتاحة
    medicines = Medicine.query.filter(Medicine.quantity > 0).all()
    return render_template('new_sale.html', medicines=medicines)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)

# -*- coding: utf-8 -*-
"""
تطبيق صيدلية مبسط - Simple Pharmacy App
"""

from flask import Flask, render_template_string

app = Flask(__name__)

# قالب HTML مبسط
template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الصيدلية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: 50px;
            padding: 40px;
        }
        .welcome-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-10px);
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="main-card">
                    <div class="text-center mb-5">
                        <h1 class="display-3 welcome-text mb-4">
                            <i class="fas fa-pills me-3"></i>
                            نظام إدارة الصيدلية
                        </h1>
                        <p class="lead text-muted">
                            نظام شامل ومتطور لإدارة الصيدليات بكفاءة عالية
                        </p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="feature-card">
                                <i class="fas fa-pills fa-3x mb-3"></i>
                                <h4>إدارة الأدوية</h4>
                                <p>إضافة وتعديل وحذف الأدوية مع جميع التفاصيل</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="feature-card">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <h4>نظام المبيعات</h4>
                                <p>إنشاء فواتير البيع وإدارة العمليات التجارية</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="feature-card">
                                <i class="fas fa-warehouse fa-3x mb-3"></i>
                                <h4>إدارة المخزون</h4>
                                <p>متابعة الكميات وتنبيهات النفاد</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="feature-card">
                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                <h4>التقارير</h4>
                                <p>تقارير شاملة للمبيعات والأرباح</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-5">
                        <h3 class="mb-4">مميزات التطبيق</h3>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                    <h5>واجهة عربية احترافية</h5>
                                    <p class="text-muted">تصميم حديث وسهل الاستخدام</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <i class="fas fa-mobile-alt text-primary fa-2x mb-2"></i>
                                    <h5>متوافق مع الأجهزة</h5>
                                    <p class="text-muted">يعمل على جميع الأجهزة والشاشات</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <i class="fas fa-shield-alt text-warning fa-2x mb-2"></i>
                                    <h5>آمن وموثوق</h5>
                                    <p class="text-muted">حماية عالية للبيانات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-5">
                        <a href="/medicines" class="btn btn-custom btn-lg me-3">
                            <i class="fas fa-pills me-2"></i>
                            إدارة الأدوية
                        </a>
                        <a href="/sales" class="btn btn-custom btn-lg">
                            <i class="fas fa-shopping-cart me-2"></i>
                            المبيعات
                        </a>
                    </div>
                    
                    <div class="text-center mt-4">
                        <p class="text-muted">
                            <i class="fas fa-code me-2"></i>
                            تم التطوير بواسطة Python Flask & Bootstrap
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(template)

@app.route('/medicines')
def medicines():
    return render_template_string("""
    <div style="text-align: center; padding: 50px; font-family: 'Cairo', sans-serif;">
        <h1>صفحة الأدوية</h1>
        <p>هذه الصفحة قيد التطوير...</p>
        <a href="/" style="color: #667eea;">العودة للرئيسية</a>
    </div>
    """)

@app.route('/sales')
def sales():
    return render_template_string("""
    <div style="text-align: center; padding: 50px; font-family: 'Cairo', sans-serif;">
        <h1>صفحة المبيعات</h1>
        <p>هذه الصفحة قيد التطوير...</p>
        <a href="/" style="color: #667eea;">العودة للرئيسية</a>
    </div>
    """)

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق الصيدلية...")
    print("🌐 التطبيق متاح على: http://localhost:5000")
    print("⏹️  للإيقاف اضغط Ctrl+C")
    app.run(debug=True, host='0.0.0.0', port=5000)

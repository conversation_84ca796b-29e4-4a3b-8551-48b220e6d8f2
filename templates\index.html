{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-4 text-center mb-4">
            <i class="fas fa-pills text-primary me-3"></i>
            مرحباً بك في نظام إدارة الصيدلية
        </h1>
        <p class="lead text-center text-muted">
            نظام شامل لإدارة الأدوية والمبيعات والمخزون بكفاءة عالية
        </p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card text-center">
            <div class="stats-icon mb-3">
                <i class="fas fa-pills fa-3x"></i>
            </div>
            <div class="stats-number">{{ total_medicines }}</div>
            <div class="stats-label">إجمالي الأدوية</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-icon mb-3">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
            </div>
            <div class="stats-number">{{ low_stock_count }}</div>
            <div class="stats-label">أدوية منخفضة المخزون</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-icon mb-3">
                <i class="fas fa-shopping-cart fa-3x"></i>
            </div>
            <div class="stats-number">{{ today_sales }}</div>
            <div class="stats-label">مبيعات اليوم</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card text-center" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            <div class="stats-icon mb-3">
                <i class="fas fa-calendar-times fa-3x"></i>
            </div>
            <div class="stats-number">{{ expired_count }}</div>
            <div class="stats-label">أدوية منتهية الصلاحية</div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_medicine') }}" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                            إضافة دواء جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-success w-100 py-3">
                            <i class="fas fa-shopping-cart fa-2x d-block mb-2"></i>
                            عملية بيع جديدة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('medicines') }}" class="btn btn-info w-100 py-3">
                            <i class="fas fa-search fa-2x d-block mb-2"></i>
                            البحث عن دواء
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-warning w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الأدوية منخفضة المخزون -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    أدوية منخفضة المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_medicines %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الدواء</th>
                                    <th>الكمية المتاحة</th>
                                    <th>الحد الأدنى</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in low_stock_medicines %}
                                <tr>
                                    <td>{{ medicine.name }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ medicine.quantity }}</span>
                                    </td>
                                    <td>{{ medicine.min_quantity }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('medicines') }}?filter=low_stock" class="btn btn-outline-primary">
                            عرض جميع الأدوية منخفضة المخزون
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>جميع الأدوية متوفرة بكميات كافية</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- المبيعات الأخيرة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    المبيعات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>#{{ sale.id }}</td>
                                    <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ "%.2f"|format(sale.final_amount) }} ج.م</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="#" class="btn btn-outline-primary">
                            عرض جميع المبيعات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>لا توجد مبيعات حتى الآن</p>
                        <a href="#" class="btn btn-primary">
                            إنشاء أول عملية بيع
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

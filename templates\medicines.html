{% extends "base.html" %}

{% block title %}إدارة الأدوية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-pills text-primary me-2"></i>
                إدارة الأدوية
            </h2>
            <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة دواء جديد
            </a>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">البحث</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="ابحث بالاسم أو الاسم العلمي أو الباركود..." 
                                   value="{{ search }}">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع الفئات</option>
                            <option value="مسكنات">مسكنات</option>
                            <option value="مضادات حيوية">مضادات حيوية</option>
                            <option value="فيتامينات">فيتامينات</option>
                            <option value="أدوية القلب">أدوية القلب</option>
                            <option value="أدوية السكري">أدوية السكري</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filter" class="form-label">الفلتر</label>
                        <select class="form-select" id="filter" name="filter">
                            <option value="">جميع الأدوية</option>
                            <option value="low_stock">منخفضة المخزون</option>
                            <option value="expired">منتهية الصلاحية</option>
                            <option value="expiring_soon">تنتهي قريباً</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الأدوية -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأدوية
                    <span class="badge bg-light text-dark ms-2">{{ medicines.total }} دواء</span>
                </h5>
            </div>
            <div class="card-body">
                {% if medicines.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الاسم العلمي</th>
                                    <th>الشركة</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in medicines.items %}
                                <tr>
                                    <td>
                                        <strong>{{ medicine.name }}</strong>
                                        {% if medicine.barcode %}
                                            <br><small class="text-muted">{{ medicine.barcode }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ medicine.scientific_name or '-' }}</td>
                                    <td>{{ medicine.company or '-' }}</td>
                                    <td>
                                        {% if medicine.category %}
                                            <span class="badge bg-secondary">{{ medicine.category }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(medicine.price) }} ج.م</strong>
                                        {% if medicine.cost_price %}
                                            <br><small class="text-muted">التكلفة: {{ "%.2f"|format(medicine.cost_price) }} ج.م</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if medicine.is_low_stock %}
                                            <span class="badge bg-danger">{{ medicine.quantity }}</span>
                                        {% elif medicine.quantity < medicine.min_quantity * 2 %}
                                            <span class="badge bg-warning">{{ medicine.quantity }}</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ medicine.quantity }}</span>
                                        {% endif %}
                                        <br><small class="text-muted">الحد الأدنى: {{ medicine.min_quantity }}</small>
                                    </td>
                                    <td>
                                        {% if medicine.expiry_date %}
                                            {% if medicine.is_expired %}
                                                <span class="badge bg-danger">{{ medicine.expiry_date.strftime('%Y-%m-%d') }}</span>
                                                <br><small class="text-danger">منتهي الصلاحية</small>
                                            {% else %}
                                                {{ medicine.expiry_date.strftime('%Y-%m-%d') }}
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if medicine.is_expired %}
                                            <span class="badge bg-danger">منتهي الصلاحية</span>
                                        {% elif medicine.is_low_stock %}
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        {% else %}
                                            <span class="badge bg-success">متاح</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    data-bs-toggle="modal" data-bs-target="#viewModal{{ medicine.id }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="#" class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDelete({{ medicine.id }}, '{{ medicine.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <!-- Modal لعرض تفاصيل الدواء -->
                                <div class="modal fade" id="viewModal{{ medicine.id }}" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">تفاصيل الدواء: {{ medicine.name }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>الاسم:</strong> {{ medicine.name }}</p>
                                                        <p><strong>الاسم العلمي:</strong> {{ medicine.scientific_name or '-' }}</p>
                                                        <p><strong>الشركة:</strong> {{ medicine.company or '-' }}</p>
                                                        <p><strong>الفئة:</strong> {{ medicine.category or '-' }}</p>
                                                        <p><strong>الباركود:</strong> {{ medicine.barcode or '-' }}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>السعر:</strong> {{ "%.2f"|format(medicine.price) }} ج.م</p>
                                                        <p><strong>سعر التكلفة:</strong> {{ "%.2f"|format(medicine.cost_price) }} ج.م</p>
                                                        <p><strong>الكمية المتاحة:</strong> {{ medicine.quantity }}</p>
                                                        <p><strong>الحد الأدنى:</strong> {{ medicine.min_quantity }}</p>
                                                        <p><strong>تاريخ الانتهاء:</strong> {{ medicine.expiry_date.strftime('%Y-%m-%d') if medicine.expiry_date else '-' }}</p>
                                                    </div>
                                                </div>
                                                {% if medicine.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <p><strong>الوصف:</strong></p>
                                                            <p class="text-muted">{{ medicine.description }}</p>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                <a href="#" class="btn btn-primary">تعديل</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if medicines.pages > 1 %}
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination justify-content-center">
                                {% if medicines.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('medicines', page=medicines.prev_num, search=search) }}">السابق</a>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in medicines.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != medicines.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('medicines', page=page_num, search=search) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if medicines.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('medicines', page=medicines.next_num, search=search) }}">التالي</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-pills fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أدوية</h4>
                        <p class="text-muted">لم يتم العثور على أي أدوية تطابق معايير البحث</p>
                        <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول دواء
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(medicineId, medicineName) {
    if (confirm('هل أنت متأكد من حذف الدواء "' + medicineName + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب الحذف
        fetch('/medicines/delete/' + medicineId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الدواء');
            }
        })
        .catch(error => {
            alert('حدث خطأ أثناء حذف الدواء');
        });
    }
}
</script>
{% endblock %}

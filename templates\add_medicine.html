{% extends "base.html" %}

{% block title %}إضافة دواء جديد - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-plus-circle text-primary me-2"></i>
                إضافة دواء جديد
            </h2>
            <a href="{{ url_for('medicines') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للأدوية
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-pills me-2"></i>
                    بيانات الدواء
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="medicineForm">
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم الدواء <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">أدخل الاسم التجاري للدواء</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="scientific_name" class="form-label">الاسم العلمي</label>
                                <input type="text" class="form-control" id="scientific_name" name="scientific_name">
                                <div class="form-text">الاسم العلمي أو المادة الفعالة</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company" class="form-label">الشركة المصنعة</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>
                            
                            <div class="mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">اختر الفئة</option>
                                    <option value="مسكنات">مسكنات</option>
                                    <option value="مضادات حيوية">مضادات حيوية</option>
                                    <option value="فيتامينات">فيتامينات</option>
                                    <option value="أدوية القلب">أدوية القلب</option>
                                    <option value="أدوية السكري">أدوية السكري</option>
                                    <option value="أدوية الضغط">أدوية الضغط</option>
                                    <option value="مضادات الالتهاب">مضادات الالتهاب</option>
                                    <option value="أدوية الجهاز الهضمي">أدوية الجهاز الهضمي</option>
                                    <option value="أدوية الجهاز التنفسي">أدوية الجهاز التنفسي</option>
                                    <option value="مكملات غذائية">مكملات غذائية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="barcode" class="form-label">الباركود</label>
                                <input type="text" class="form-control" id="barcode" name="barcode">
                                <div class="form-text">رقم الباركود (اختياري)</div>
                            </div>
                        </div>
                        
                        <!-- المعلومات المالية والمخزون -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-dollar-sign me-2"></i>
                                المعلومات المالية والمخزون
                            </h6>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" required>
                                    <span class="input-group-text">ج.م</span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                           step="0.01" min="0">
                                    <span class="input-group-text">ج.م</span>
                                </div>
                                <div class="form-text">سعر شراء الدواء من المورد</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="quantity" class="form-label">الكمية المتاحة</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="0" value="0">
                                <div class="form-text">عدد الوحدات المتاحة في المخزون</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="min_quantity" class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" id="min_quantity" name="min_quantity" 
                                       min="1" value="10">
                                <div class="form-text">الحد الأدنى للتنبيه عند نفاد المخزون</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                                <div class="form-text">تاريخ انتهاء صلاحية الدواء</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الوصف -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-file-alt me-2"></i>
                                معلومات إضافية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف والملاحظات</label>
                                <textarea class="form-control" id="description" name="description" rows="4"
                                          placeholder="أدخل وصف الدواء، دواعي الاستعمال، الجرعة، أو أي ملاحظات أخرى..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الدواء
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="saveAndAddAnother()">
                                        <i class="fas fa-plus me-2"></i>
                                        حفظ وإضافة آخر
                                    </button>
                                </div>
                                <div>
                                    <button type="reset" class="btn btn-outline-warning me-2">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <a href="{{ url_for('medicines') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معاينة البيانات -->
<div class="row mt-4 justify-content-center">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة البيانات
                </h6>
            </div>
            <div class="card-body">
                <div class="row" id="preview">
                    <div class="col-12 text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <p>ستظهر معاينة البيانات هنا أثناء الكتابة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// معاينة البيانات أثناء الكتابة
function updatePreview() {
    const name = document.getElementById('name').value;
    const scientificName = document.getElementById('scientific_name').value;
    const company = document.getElementById('company').value;
    const category = document.getElementById('category').value;
    const price = document.getElementById('price').value;
    const costPrice = document.getElementById('cost_price').value;
    const quantity = document.getElementById('quantity').value;
    const minQuantity = document.getElementById('min_quantity').value;
    const expiryDate = document.getElementById('expiry_date').value;
    
    if (name || scientificName || price) {
        const preview = document.getElementById('preview');
        const profit = price && costPrice ? (price - costPrice).toFixed(2) : 0;
        const profitMargin = price && costPrice ? (((price - costPrice) / costPrice) * 100).toFixed(1) : 0;
        
        preview.innerHTML = `
            <div class="col-md-6">
                <h6 class="text-primary">معلومات الدواء</h6>
                <p><strong>الاسم:</strong> ${name || 'غير محدد'}</p>
                <p><strong>الاسم العلمي:</strong> ${scientificName || 'غير محدد'}</p>
                <p><strong>الشركة:</strong> ${company || 'غير محدد'}</p>
                <p><strong>الفئة:</strong> ${category || 'غير محدد'}</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">المعلومات المالية</h6>
                <p><strong>سعر البيع:</strong> ${price ? price + ' ج.م' : 'غير محدد'}</p>
                <p><strong>سعر التكلفة:</strong> ${costPrice ? costPrice + ' ج.م' : 'غير محدد'}</p>
                <p><strong>الربح:</strong> ${profit ? profit + ' ج.م' : 'غير محدد'}</p>
                <p><strong>هامش الربح:</strong> ${profitMargin ? profitMargin + '%' : 'غير محدد'}</p>
                <p><strong>الكمية:</strong> ${quantity || '0'} وحدة</p>
                <p><strong>تاريخ الانتهاء:</strong> ${expiryDate || 'غير محدد'}</p>
            </div>
        `;
    }
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['name', 'scientific_name', 'company', 'category', 'price', 'cost_price', 'quantity', 'min_quantity', 'expiry_date'];
    inputs.forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
        document.getElementById(id).addEventListener('change', updatePreview);
    });
});

// حفظ وإضافة آخر
function saveAndAddAnother() {
    const form = document.getElementById('medicineForm');
    const formData = new FormData(form);
    formData.append('add_another', 'true');
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إعادة تعيين النموذج
            form.reset();
            updatePreview();
            // إظهار رسالة نجاح
            showAlert('تم حفظ الدواء بنجاح! يمكنك إضافة دواء آخر.', 'success');
            // التركيز على حقل الاسم
            document.getElementById('name').focus();
        } else {
            showAlert('حدث خطأ أثناء حفظ الدواء: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ أثناء حفظ الدواء', 'error');
    });
}

// إظهار التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// التحقق من صحة النموذج
document.getElementById('medicineForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const price = document.getElementById('price').value;
    
    if (!name) {
        e.preventDefault();
        showAlert('يرجى إدخال اسم الدواء', 'error');
        document.getElementById('name').focus();
        return;
    }
    
    if (!price || price <= 0) {
        e.preventDefault();
        showAlert('يرجى إدخال سعر صحيح للدواء', 'error');
        document.getElementById('price').focus();
        return;
    }
});
</script>
{% endblock %}

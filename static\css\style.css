/* نمط مخصص لتطبيق الصيدلية */
/* Custom styles for Pharmacy Management System */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* تحسينات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* تحسينات الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 10px 20px;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 20px;
    font-weight: 600;
}

/* تحسينات الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transition: var(--transition);
}

.stats-card:hover::before {
    transform: scale(1.5);
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.stats-label {
    font-size: 1.1rem;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.stats-icon {
    position: relative;
    z-index: 1;
}

/* تحسينات شريط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 5px;
    margin: 0 5px;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

/* تحسينات التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* تحسينات الشارات */
.badge {
    border-radius: 20px;
    padding: 8px 12px;
    font-weight: 600;
}

/* تحسينات الأزرار الصغيرة */
.btn-sm {
    padding: 5px 10px;
    font-size: 0.875rem;
}

/* تحسينات المودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تحسينات التنقل بين الصفحات */
.pagination .page-link {
    border-radius: 5px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .stats-card {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 20px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar, .btn, .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .stats-card {
        background: white !important;
        color: black !important;
        border: 1px solid #ddd;
    }
}

/* تحسينات إضافية للنصوص العربية */
.text-arabic {
    font-family: 'Cairo', sans-serif;
    text-align: right;
    direction: rtl;
}

/* تحسينات للأيقونات */
.fa, .fas, .far {
    margin-left: 8px;
}

/* تحسينات للروابط */
a {
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    text-decoration: none;
}

/* تحسينات للقوائم المنسدلة */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

.dropdown-item {
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: translateX(-5px);
}

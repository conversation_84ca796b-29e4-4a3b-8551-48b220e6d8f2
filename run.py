# -*- coding: utf-8 -*-
"""
ملف تشغيل تطبيق الصيدلية
Pharmacy Application Runner
"""

try:
    from app import app, db
    
    if __name__ == '__main__':
        print("بدء تشغيل تطبيق الصيدلية...")
        print("Starting Pharmacy Management System...")
        
        # إنشاء قاعدة البيانات
        with app.app_context():
            db.create_all()
            print("تم إنشاء قاعدة البيانات بنجاح")
            print("Database created successfully")
        
        print("التطبيق يعمل على: http://localhost:5000")
        print("Application running on: http://localhost:5000")
        print("للإيقاف اضغط Ctrl+C")
        print("Press Ctrl+C to stop")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المتطلبات أولاً:")
    print("pip install Flask Flask-SQLAlchemy Flask-WTF")
    
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    print(f"Application error: {e}")
